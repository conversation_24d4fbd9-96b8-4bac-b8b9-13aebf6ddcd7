/**
 * Test class for ContactDetailsSectionController
 * Tests the hasMobileChangedInPastMonths method with various scenarios
 */
@isTest
public class ContactDetailsSectionControllerTest {
    
    @testSetup
    static void setupTestData() {
        // Create test accounts and contacts
        Account individualAccount = createIndividualAccount();
        Account organizationAccount = createOrganizationAccount();
        
        // Create contacts for the accounts
        Contact individualContact = createContactForAccount(individualAccount.Id, 'Individual');
        Contact organizationContact = createContactForAccount(organizationAccount.Id, 'Organization');
        
        // Set primary contact for individual account
        individualAccount.FinServ__PrimaryContact__c = individualContact.Id;
        update individualAccount;
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_ContactId_NoChanges() {
        // Test with Contact ID when no mobile changes exist
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];
        
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 3);
        Test.stopTest();
        
        System.assertEquals(false, result, 'Should return false when no mobile changes exist');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_AccountId_NoChanges() {
        // Test with Account ID when no mobile changes exist
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Individual Account' LIMIT 1];
        
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testAccount.Id, 3);
        Test.stopTest();
        
        System.assertEquals(false, result, 'Should return false when no mobile changes exist');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_ContactId_WithRecentChanges() {
        // Test with Contact ID when recent mobile changes exist
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];
        
        // Update mobile phone to create history
        testContact.MobilePhone = '**********';
        update testContact;
        
        // Update again to create another history entry
        testContact.MobilePhone = '**********';
        update testContact;
        
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 3);
        Test.stopTest();
        
        System.assertEquals(true, result, 'Should return true when recent mobile changes exist');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_AccountId_WithRecentChanges() {
        // Test with Account ID when recent mobile changes exist
        Account testAccount = [SELECT Id, FinServ__PrimaryContact__c FROM Account WHERE Name = 'Test Individual Account' LIMIT 1];
        Contact testContact = [SELECT Id FROM Contact WHERE Id = :testAccount.FinServ__PrimaryContact__c];
        
        // Update mobile phone to create history
        testContact.MobilePhone = '**********';
        update testContact;
        
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testAccount.Id, 3);
        Test.stopTest();
        
        System.assertEquals(true, result, 'Should return true when recent mobile changes exist for account contact');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_DifferentMonthsBack() {
        // Test with different monthsBack values
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];
        
        // Update mobile phone to create history
        testContact.MobilePhone = '**********';
        update testContact;
        
        Test.startTest();
        Boolean result1Month = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 1);
        Boolean result6Months = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 6);
        Boolean result12Months = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 12);
        Test.stopTest();
        
        System.assertEquals(true, result1Month, 'Should return true for 1 month back');
        System.assertEquals(true, result6Months, 'Should return true for 6 months back');
        System.assertEquals(true, result12Months, 'Should return true for 12 months back');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_InvalidRecordId() {
        // Test with invalid record ID
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths('invalid_id', 3);
        Test.stopTest();
        
        System.assertEquals(false, result, 'Should return false for invalid record ID');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_NullRecordId() {
        // Test with null record ID
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(null, 3);
        Test.stopTest();
        
        System.assertEquals(false, result, 'Should return false for null record ID');
    }
    
    @isTest
    static void testHasMobileChangedInPastMonths_AccountWithoutPrimaryContact() {
        // Test with organization account that doesn't have primary contact set
        Account orgAccount = [SELECT Id FROM Account WHERE Name = 'Test Organization Account' LIMIT 1];
        Contact orgContact = [SELECT Id FROM Contact WHERE LastName = 'Organization Test' LIMIT 1];

        // Update mobile phone to create history
        orgContact.MobilePhone = '**********';
        update orgContact;

        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(orgAccount.Id, 3);
        Test.stopTest();

        System.assertEquals(true, result, 'Should return true when contact has mobile changes even without primary contact set');
    }

    @isTest
    static void testHasMobileChangedInPastMonths_ZeroMonthsBack() {
        // Test with 0 months back (should check from today)
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];

        // Update mobile phone to create history
        testContact.MobilePhone = '**********';
        update testContact;

        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 0);
        Test.stopTest();

        System.assertEquals(true, result, 'Should return true for changes made today with 0 months back');
    }

    @isTest
    static void testHasMobileChangedInPastMonths_EmptyStringRecordId() {
        // Test with empty string record ID
        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths('', 3);
        Test.stopTest();

        System.assertEquals(false, result, 'Should return false for empty string record ID');
    }

    @isTest
    static void testHasMobileChangedInPastMonths_AccountWithoutContacts() {
        // Create an account without any contacts
        Account testAccount = new Account();
        testAccount.Name = 'Account Without Contacts';
        testAccount.RecordTypeId = ConstantsGlobal.ACCOUNT_RT_INDIVIDUAL_ID;
        insert testAccount;

        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testAccount.Id, 3);
        Test.stopTest();

        System.assertEquals(false, result, 'Should return false for account without contacts');
    }

    @isTest
    static void testHasMobileChangedInPastMonths_MultipleContactHistory() {
        // Test with multiple mobile phone changes
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];

        // Create multiple mobile phone changes
        testContact.MobilePhone = '**********';
        update testContact;

        testContact.MobilePhone = '**********';
        update testContact;

        testContact.MobilePhone = '**********';
        update testContact;

        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, 3);
        Test.stopTest();

        System.assertEquals(true, result, 'Should return true when multiple mobile changes exist');
    }

    @isTest
    static void testHasMobileChangedInPastMonths_NegativeMonthsBack() {
        // Test with negative months back value
        Contact testContact = [SELECT Id FROM Contact WHERE LastName = 'Individual Test' LIMIT 1];

        // Update mobile phone to create history
        testContact.MobilePhone = '**********';
        update testContact;

        Test.startTest();
        Boolean result = ContactDetailsSectionController.hasMobileChangedInPastMonths(testContact.Id, -3);
        Test.stopTest();

        System.assertEquals(false, result, 'Should return false for negative months back (future date)');
    }
    
    // Helper methods for creating test data
    private static Account createIndividualAccount() {
        Account acc = new Account();
        acc.Name = 'Test Individual Account';
        acc.RecordTypeId = ConstantsGlobal.ACCOUNT_RT_INDIVIDUAL_ID;
        insert acc;
        return acc;
    }
    
    private static Account createOrganizationAccount() {
        Account acc = new Account();
        acc.Name = 'Test Organization Account';
        acc.RecordTypeId = ConstantsGlobal.ACCOUNT_RT_ORGANISATION_ID;
        insert acc;
        return acc;
    }
    
    private static Contact createContactForAccount(Id accountId, String type) {
        Contact con = new Contact();
        con.FirstName = type;
        con.LastName = type + ' Test';
        con.AccountId = accountId;
        con.Email = type.toLowerCase() + '@test.com';
        con.MobilePhone = '**********';
        
        if (type == 'Individual') {
            con.RecordTypeId = ConstantsGlobal.CONTACT_RT_INDIVIDUAL_ID;
        } else {
            con.RecordTypeId = ConstantsGlobal.CONTACT_RT_ORGANISATION_ID;
        }
        
        insert con;
        return con;
    }
}
