/**
* Author		: <PERSON><PERSON> (Accenture) 
* Date			: June 30, 2020
* Description	: Apex controller used in customEmailInputValidation, customMobileInputValidation, 
				  accountContactDetailsIndivInternal, accountContactDetailsIndivExternal, accountContactDetailsOrgInternal, and accountContactDetailsOrgExternal
* 
**/
public without sharing class ContactDetailsSectionController {

    @AuraEnabled(cacheable=true)
    public static Map<String,Object> getEmailWebServiceInterface(){
        Map<String,Object> wsiResponse = new Map<String,Object>();
        try{
            Web_Service_Interface__c wsInterface = Web_Service_Interface__c.getInstance(ConstantsExperian.EXPERIAN_WSI_EMAIL_VALIDATOR);
            
            //add default values to avoid breaking the code when custom setting fields are null
            String url = (wsInterface.Endpoint__c != '') ? wsInterface.Endpoint__c : '';
            String authToken = (wsInterface.Authorization_Token__c != '') ? wsInterface.Authorization_Token__c : '';
            Decimal timeout = wsInterface.Request_Timeout__c != null ? wsInterface.Request_Timeout__c : 5000; 
            // convert milliseconds to seconds       
            if(timeout != null){
                timeout = (timeout/1000).round(System.RoundingMode.DOWN);
            }
            
            wsiResponse.put('url', url);
            wsiResponse.put('authToken', authToken);
            wsiResponse.put('timeout', timeout);
            wsiResponse.put('globalSwitch', wsInterface.Disable_Web_Service__c);
            wsiResponse.put('experianSwitch', wsInterface.Disable_Experian_Verify_Button__c); // SFP-35634 by J.Mendoza
        } catch (Exception e){ ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'getEmailWebServiceInterface', e, e.getMessage(), '', 0); ApplicationLogUtility.commitLog(); }
        return wsiResponse;
    }
    
    @AuraEnabled(cacheable=true)
    public static Map<String,Object> getMobileWebServiceInterface (){
        Map<String,Object> wsiResponse = new Map<String,Object>();
        try{
            Web_Service_Interface__c wsInterface = Web_Service_Interface__c.getInstance(ConstantsExperian.EXPERIAN_WSI_MOBILE_VALIDATOR);
            
            String url = (wsInterface.Endpoint__c != '') ? wsInterface.Endpoint__c : '';
            String authToken = (wsInterface.Authorization_Token__c != '') ? wsInterface.Authorization_Token__c : '';
            Decimal timeout = wsInterface.Request_Timeout__c;
            // convert milliseconds to seconds        
            if(timeout != null){
                timeout = (timeout/1000).round(System.RoundingMode.DOWN);
            }
            
            wsiResponse.put('url', url);
            wsiResponse.put('authToken', authToken);
            wsiResponse.put('timeout', timeout);
            wsiResponse.put('globalSwitch', wsInterface.Disable_Web_Service__c);
            wsiResponse.put('experianSwitch', wsInterface.Disable_Experian_Verify_Button__c); // SFP-35634 by J.Mendoza
        } catch (Exception e){ ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'getMobileWebServiceInterface ', e, e.getMessage(), '', 0); ApplicationLogUtility.commitLog(); }
        return wsiResponse;
    }

    @AuraEnabled(cacheable=true)
    public static Boolean getUserRecordAccess(String userId, String recordId){
        UserRecordAccessDAI userRecordAccessDAO = (UserRecordAccessDAI) DAOFactory.getDAO(UserRecordAccess.SObjectType);
        List<UserRecordAccess> uraList = userRecordAccessDAO.getUserRecordAccess((Id)userId, recordId);
        
        Boolean hasEditAccess = false;
        if(!uraList.isEmpty()){
            hasEditAccess = uraList[0].HasEditAccess;
        }
        return hasEditAccess;
    }

    //SFP-32688 by Heither Ann Ballero
    //Handles calculation of months in between the today's date and the Validation Timestamp
    @AuraEnabled(cacheable=true)
    public static Integer calculateMonthsInBetween(DateTime verificationTimeStamp){
        Integer numberOfMonths;
        try{
            DateTime dt = System.now();
            Date dateToday = Date.newinstance(dt.year(), dt.month(), dt.day());
            Date verificationDate = date.newinstance(verificationTimeStamp.year(), verificationTimeStamp.month(), verificationTimeStamp.day());
            numberOfMonths = verificationDate.monthsBetween(dateToday);
        } catch (Exception e){ ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'calculateMonthsInBetween ', e, e.getMessage(), '', 0); ApplicationLogUtility.commitLog(); }
        return numberOfMonths;
    }
    
    //Author: Eugene Ray Perfecio
    //Wrapper class used to store Experian Verification responses
    public class ExperianVerificationDetails{
        @AuraEnabled
        public String emailValidationMessage; //Experian message
        @AuraEnabled
        public String emailValidationStatus; //Experian status
        @AuraEnabled
        public Datetime emailValidationTimestamp; //Experian timestamp
        @AuraEnabled
        public Integer monthsInBetweenLastValidation = 0; //number of months from last validation
        @AuraEnabled
        public String mobileValidationMessage;
        @AuraEnabled
        public String mobileValidationStatus;
        @AuraEnabled
        public Datetime mobileValidationTimestamp;
        @AuraEnabled
        public Integer monthsInBetweenLastValidationForMobile = 0;
    }
    
    //Author: Eugene Ray Perfecio
    //Gets verification details for Id Check
    @AuraEnabled
    public static ExperianVerificationDetails getVerificationDetailsForIdCheck(Id recordId){
        ExperianVerificationDetails expVerifDetails = new ExperianVerificationDetails();
        AccountDAI accountDAO = (AccountDAI) DAOFactory.getDAO(Account.SObjectType);
        try{
            Account acc = accountDAO.findAccountByID(recordId);
            //if account is individual, we use the fields from the associated contact record
            if(acc.RecordTypeId == ConstantsGlobal.ACCOUNT_RT_INDIVIDUAL_ID){
                //Email validation fields
                expVerifDetails.emailValidationMessage = acc.FinServ__PrimaryContact__r.Email_Validation_Message__c;
                expVerifDetails.emailValidationStatus = acc.FinServ__PrimaryContact__r.Email_Validation_Status__c;
                expVerifDetails.emailValidationTimestamp = acc.FinServ__PrimaryContact__r.Email_Validation_Timestamp__c;
                if(acc.FinServ__PrimaryContact__r.Email_Validation_Timestamp__c != null){
                    expVerifDetails.monthsInBetweenLastValidation = calculateMonthsInBetween(acc.FinServ__PrimaryContact__r.Email_Validation_Timestamp__c);
                }
                //Mobile Validation Details
                expVerifDetails.mobileValidationMessage = acc.FinServ__PrimaryContact__r.Mobile_Phone_Validation_Message__c;
                expVerifDetails.mobileValidationStatus = acc.FinServ__PrimaryContact__r.Mobile_Phone_Validation_Status__c;
                expVerifDetails.mobileValidationTimestamp = acc.FinServ__PrimaryContact__r.Mobile_Phone_Validation_Timestamp__c;
                if(acc.FinServ__PrimaryContact__r.Mobile_Phone_Validation_Timestamp__c != null){
                    expVerifDetails.monthsInBetweenLastValidationForMobile = calculateMonthsInBetween(acc.FinServ__PrimaryContact__r.Mobile_Phone_Validation_Timestamp__c);
                }
            }
            //if account record type is organisation, we fetch the details directly from the account
            else if(acc.RecordTypeId == ConstantsGlobal.ACCOUNT_RT_ORGANISATION_ID){
                //Email validation fields
                expVerifDetails.emailValidationMessage = acc.Email_Validation_Message__c;
                expVerifDetails.emailValidationStatus = acc.Email_Validation_Status__c;
                expVerifDetails.emailValidationTimestamp = acc.Email_Validation_Timestamp__c;
                if(acc.Email_Validation_Timestamp__c != null){
                    expVerifDetails.monthsInBetweenLastValidation = calculateMonthsInBetween(acc.Email_Validation_Timestamp__c);
                }

                //Mobile Validation 
                expVerifDetails.mobileValidationMessage = acc.Mobile_Phone_Validation_Message__c;
                expVerifDetails.mobileValidationStatus = acc.Mobile_Phone_Validation_Status__c;
                expVerifDetails.mobileValidationTimestamp = acc.Mobile_Phone_Validation_Timestamp__c;
                if(acc.Mobile_Phone_Validation_Timestamp__c != null){
                    expVerifDetails.monthsInBetweenLastValidationForMobile = calculateMonthsInBetween(acc.Mobile_Phone_Validation_Timestamp__c);
                }
            }
        }catch(Exception ex){ ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'getVerificationDetialsForIdCheck ', ex, ex.getMessage(), '', 0); ApplicationLogUtility.commitLog(); }
        return expVerifDetails;
    }
    
    //Author: Eugene Ray Perfecio
    //Updates verification details from Id check flow
    @AuraEnabled
    public static void updateVerificationDetailsForIdCheck(Id recordId, String message, String status, Boolean isEmail){
        try{
            AccountDAI accountDAO = (AccountDAI) DAOFactory.getDAO(Account.SObjectType);
            ContactDAI contactDAO = (ContactDAI) DAOFactory.getDAO(Contact.SObjectType);
            
            //query for account
            Account acc = accountDAO.findAccountByID(recordId);
            //if account record type is Individual
            if(acc.RecordTypeId == ConstantsGlobal.ACCOUNT_RT_INDIVIDUAL_ID){
                //we update the contact
                List<Contact> conList = contactDAO.getContactsByAccountIds(new Set<Id>{recordId});
                for(Contact con: conList){
                    //check what component is calling the method
                    if(isEmail){
                        con.Email_Validation_Message__c = message;
                        con.Email_Validation_Status__c = status;
                        con.Email_Validation_Timestamp__c = Datetime.now();
                    }
                    else{
                        con.Mobile_Phone_Validation_Message__c = message;
                        con.Mobile_Phone_Validation_Status__c = status;
                        con.Mobile_Phone_Validation_Timestamp__c = Datetime.now();
                    }
                }
                contactDAO.updateRecords(conList, true);
            }
            //if account record type is Organisation, we update the account
            else if(acc.RecordTypeId == ConstantsGlobal.ACCOUNT_RT_ORGANISATION_ID){
                //check what component is calling the method
                if(isEmail){
                    acc.Email_Validation_Message__c = message;
                    acc.Email_Validation_Status__c = status;
                    acc.Email_Validation_Timestamp__c = Datetime.now();
                }
                else{
                    acc.Mobile_Phone_Validation_Message__c = message;
                    acc.Mobile_Phone_Validation_Status__c = status;
                    acc.Mobile_Phone_Validation_Timestamp__c = Datetime.now();
                }
                accountDAO.updateRecords(new List<Account>{acc}, true);
            }
        }catch(Exception ex){ ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'updateVerificationDetailsForIdCheck ', ex, ex.getMessage(), '', 0); ApplicationLogUtility.commitLog(); }
    }

    // SFP-39788 by J.Mendoza (Accenture) Aug/13/2020
    // get Contact Id to get respective Contact details from the account
    @AuraEnabled(cacheable=true)
    public static String getContactDetails(String recordId){
        AccountDAI accountDAO = (AccountDAI) DAOFactory.getDAO(Account.SObjectType);
        String conId = '';
        try{
            Account acc = accountDAO.getAccountRelContact(Id.valueOf(recordId));
            if(acc.FinServ__PrimaryContact__c != null){
                conId = acc.FinServ__PrimaryContact__c;
            } else {
                conId = acc.Contacts[0].Id;
            }
        } catch(Exception ex){ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'getContactDetails ', ex, ex.getMessage(), '', 0); ApplicationLogUtility.commitLog();}
        return conId;
    }

    @AuraEnabled(cacheable=true)
    public static Boolean hasMobileChangedInPastMonths(String recordId, Integer monthsBack){
        Boolean hasChanged = false;
        try{
            String contactId = '';

            // Determine the object type from recordId and get Contact ID
            Id recId = Id.valueOf(recordId);
            String objectType = recId.getSObjectType().getDescribe().getName();

            if(objectType == 'Contact'){
                contactId = recordId;
            } else if(objectType == 'Account'){
                contactId = getContactDetails(recordId);
            }

            if(String.isNotBlank(contactId)){
                // Calculate date based on monthsBack parameter
                Date cutoffDate = Date.today().addMonths(-monthsBack);

                List<ContactHistory> mobileHistory = [
                    SELECT Id, Field, OldValue, NewValue, CreatedDate
                    FROM ContactHistory
                    WHERE ContactId = :contactId
                    AND Field = 'MobilePhone'
                    AND CreatedDate >= :cutoffDate
                    ORDER BY CreatedDate DESC
                    LIMIT 1
                ];

                hasChanged = !mobileHistory.isEmpty();
            }
        } catch(Exception ex){
            ApplicationLogUtility.logError(ContactDetailsSectionController.class.getName(), 'hasMobileChangedInPastMonths', ex, ex.getMessage(), '', 0);
            ApplicationLogUtility.commitLog();
        }
        return hasChanged;
    }
    
}