 /*
* Author: <PERSON><PERSON> (Accenture) June 29, 2020
* JS controller for accountContactIndivInternal Component
* Custom component for Individual Internal users' Contact Details 
*/

import { LightningElement, track, wire, api } from 'lwc';  
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getUserAccess from '@salesforce/apex/ContactDetailsSectionController.getUserRecordAccess';
import getMonthsInBetween from '@salesforce/apex/ContactDetailsSectionController.calculateMonthsInBetween';
import getContactDetails from '@salesforce/apex/ContactDetailsSectionController.getContactDetails';
import { getRecord, getFieldValue } from 'lightning/uiRecordApi';

// import { refreshApex } from '@salesforce/apex';
import { updateRecord } from 'lightning/uiRecordApi';
// import { getObjectInfo } from 'lightning/uiObjectInfoApi';
// import USER_PROFILE from '@salesforce/schema/User.Profile.Name';
// import ACCOUNT_OBJECT from '@salesforce/schema/Account';

import USER_ID from '@salesforce/user/Id';
import USER_CALL_CENTER from '@salesforce/schema/User.CallCenterId';
import PRIMARY_CONTACT_ID_FIELD from '@salesforce/schema/Contact.Id';

//Email verification fields Added by Eugene Ray Perfecio (SFP-300083)
import PRIMARY_CONTACT_EMAIL_FIELD from '@salesforce/schema/Contact.Email';
import PRIMARY_CONTACT_EMAILINVALID_FIELD from '@salesforce/schema/Contact.Email_Invalid__c';
import PRIMARY_CONTACT_ALT_EMAIL_FIELD from '@salesforce/schema/Contact.Alternate_Email__c';
import PRIMARY_CONTACT_EMAIL_VAL_MESSAGE  from '@salesforce/schema/Contact.Email_Validation_Message__c';
import PRIMARY_CONTACT_EMAIL_VAL_STATUS from '@salesforce/schema/Contact.Email_Validation_Status__c';
import PRIMARY_CONTACT_EMAIL_VAL_TIMESTAMP from '@salesforce/schema/Contact.Email_Validation_Timestamp__c';
import PRIMARY_CONTACT_ALT_EMAIL_INVALID from '@salesforce/schema/Contact.Alternate_Email_Invalid__c';

//Mobile verification details
import PRIMARY_CONTACT_MOBILE_FIELD from '@salesforce/schema/Contact.MobilePhone';
import PRIMARY_CONTACT_MOBILE_VALID_MESSAGE_FIELD from '@salesforce/schema/Contact.Mobile_Phone_Validation_Message__c';
import PRIMARY_CONTACT_MOBILE_VALID_STATUS_FIELD from '@salesforce/schema/Contact.Mobile_Phone_Validation_Status__c';
import PRIMARY_CONTACT_MOBILE_VALID_DATETIME_FIELD from '@salesforce/schema/Contact.Mobile_Phone_Validation_Timestamp__c';
import PRIMARY_CONTACT_MOBILE_INVALID from '@salesforce/schema/Contact.Mobile_Invalid__c';
import PRIMARY_CONTACT_HOME_PHONE_FIELD from '@salesforce/schema/Contact.HomePhone';
import PRIMARY_CONTACT_HOME_PHONE_INVALID from '@salesforce/schema/Contact.Home_Phone_Invalid__c';
import PRIMARY_CONTACT_WORK_PHONE_FIELD from '@salesforce/schema/Contact.Phone';
import PRIMARY_CONTACT_WORK_PHONE_INVALID from '@salesforce/schema/Contact.Phone_Invalid__c';
import PRIMARY_CONTACT_FAX_FIELD from '@salesforce/schema/Contact.Fax';
import PRIMARY_CONTACT_FAX_INVALID from '@salesforce/schema/Contact.Fax_Invalid__c';

//Invalid Flags Help Text
import CONTACT_MOBILEINVALID_HELPTEXT from '@salesforce/label/c.ContactMobileInvalidHelpText';
import CONTACT_EMAILINVALID_HELPTEXT from '@salesforce/label/c.ContactEmailInvalidHelpText';
import CONTACT_ALTEMAILINVALID_HELPTEXT from '@salesforce/label/c.ContactAltEmailInvalidHelpText';
import CONTACT_HOMEPHONEINVALID_HELPTEXT from '@salesforce/label/c.ContactHomePhoneInvalidHelpText';
import CONTACT_FAXINVALID_HELPTEXT from '@salesforce/label/c.ContactFaxInvalidHelpText';
import CONTACT_WORKPHONEINVALID_HELPTEXT from '@salesforce/label/c.ContactWorkPhoneInvalidHelpText';

export default class AccountContactDetailsIndivExternal extends LightningElement {
    @api recordId;
    @api objectApiName;
    @api emailval;
    @api mobileval;
    @api isEditMode = false;
    @api disableMobileVerifyButton = false;
    @api disableEmailVerifyButton = false;
    @api editAccess;
    @api hasEmailMessage = false;
    @api hasMobileMessage = false;
    @api isCTI = false;
    
    @track loaded = true; // spinner
    @track fields;
    @track formLoaded = false;
    @track disableSaveBtn = true;
    @track disableEditBtn = true;
    @track verified = false;
    @track verifiedSuccess = false;
    @track conId; // SFP-39788

    error;
    viewLoad = false;
    contactId = ''; 
    emailInvalidField;
    chevronDown = true;
    messageFields = false;

    // Added by Eugene Ray Perfecio (SFP-30083)
    // message details for Email
    emailVerificationMessage;
    emailVerificationIconName;
    emailVerificationVariant;
    emailVerificationStyle;
    emailVerificationStatus;
    storedEmailVerificationDetails = {};
    isEmailValid = false;
    altEmailValue;
    altEmailValidity = false;
    dateDetails;
    emailInvalid = false;
    altEmailInvalid = false;
    emailVerificationTimeStamp;

    // Added by Heither Ann Ballero (SFP-30086)
    // Message details for mobile
    storedMobileVerificationDetails = {};
    mobileInvalid;
    isMobileValid = false;
    mobileVerificationMessage;
    mobileVerificationIconName;
    mobileVerificationVariant;
    mobileVerificationStyle;
    mobileVerificationStatus;
    dateDetailsForMobile;

    //Other phones
    homePhoneValue;
    homePhoneInvalid = false;
    homePhoneValidity = false;
    workPhoneValue;
    workPhoneInvalid = false;
    workPhoneValidity = false;
    faxValue;
    faxInvalid = false;
    faxValidity = false;
    
    emailInvalidPrior;
    mobileInvalidPrior;
    altEmailInvalidPrior;
    homePhoneInvalidPrior;
    workPhoneInvalidPrior;
    faxInvalidPrior;

    //Invalid Flags Helptext
    helpText = {
        CONTACT_MOBILEINVALID_HELPTEXT, CONTACT_EMAILINVALID_HELPTEXT, CONTACT_ALTEMAILINVALID_HELPTEXT,
        CONTACT_HOMEPHONEINVALID_HELPTEXT, CONTACT_FAXINVALID_HELPTEXT, CONTACT_WORKPHONEINVALID_HELPTEXT
    };

    @wire(getRecord, {
        recordId: USER_ID,
        fields: [USER_CALL_CENTER]
    })
    wiredIsUserCallCenter({error, data}){
        if(data){
            let callCenterId = getFieldValue(data, USER_CALL_CENTER);
            if(callCenterId){
                this.isCTI = true;
            }
        }
        else{
            let event = new ShowToastEvent({
                title: 'Error!',
                message: error,
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        }
    }

    @wire(getUserAccess, { userId: USER_ID, recordId: '$recordId' })
    getUserEditAccess({error, data}){
        if(data){
            this.editAccess = data;
            console.log('##JI editAccess: ', this.editAccess);
        } else {
            this.error = error;
            console.log('### Error fetching getUserEditAccess: ', error);
            // let event = new ShowToastEvent({
            //     title: 'Error!',
            //     message: error,
            //     variant: 'error',
            //     mode: 'dismissable'
            // });
            // this.dispatchEvent(event);

        }
    }

    // SFP-39788 by J.Mendoza (Accenture) Aug/13/2020
    // get Contact Id to get respective Contact details from the account
    @wire(getContactDetails, {recordId: '$recordId'})
    wiredGetContactId({error, data}){
        if(data){
            this.conId = data;
        } else if(error){
            console.log('###JI error: ', error);
            let event = new ShowToastEvent({
                title: 'Error!',
                message: error,
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
        }
    }

    @wire(getRecord, {
        recordId: '$conId',
        fields: [PRIMARY_CONTACT_ID_FIELD, PRIMARY_CONTACT_EMAIL_FIELD, PRIMARY_CONTACT_EMAILINVALID_FIELD, 
            PRIMARY_CONTACT_MOBILE_FIELD, PRIMARY_CONTACT_MOBILE_INVALID, PRIMARY_CONTACT_ALT_EMAIL_FIELD, 
            PRIMARY_CONTACT_EMAIL_VAL_MESSAGE, PRIMARY_CONTACT_MOBILE_VALID_MESSAGE_FIELD, 
            PRIMARY_CONTACT_MOBILE_VALID_STATUS_FIELD, PRIMARY_CONTACT_MOBILE_VALID_DATETIME_FIELD, 
            PRIMARY_CONTACT_EMAIL_VAL_STATUS, PRIMARY_CONTACT_EMAIL_VAL_TIMESTAMP, 
            PRIMARY_CONTACT_ALT_EMAIL_INVALID, PRIMARY_CONTACT_HOME_PHONE_FIELD, PRIMARY_CONTACT_HOME_PHONE_INVALID,
            PRIMARY_CONTACT_WORK_PHONE_FIELD, PRIMARY_CONTACT_WORK_PHONE_INVALID,  PRIMARY_CONTACT_FAX_FIELD, PRIMARY_CONTACT_FAX_INVALID] 
    }) accountContactDetails({error,data}) {
        if (data) {
            this.contactId = getFieldValue(data, PRIMARY_CONTACT_ID_FIELD);

            //for email verification
            //Added by Eugene Ray Perfecio (SFP-30083)
            this.emailval = getFieldValue(data, PRIMARY_CONTACT_EMAIL_FIELD);
            this.emailInvalid = getFieldValue(data, PRIMARY_CONTACT_EMAILINVALID_FIELD);
            this.altEmailValue = getFieldValue(data, PRIMARY_CONTACT_ALT_EMAIL_FIELD);
            this.altEmailInvalid = getFieldValue(data, PRIMARY_CONTACT_ALT_EMAIL_INVALID);
            this.emailVerificationMessage = getFieldValue(data, PRIMARY_CONTACT_EMAIL_VAL_MESSAGE);
            this.verificationDateDetails = getFieldValue(data, PRIMARY_CONTACT_EMAIL_VAL_TIMESTAMP);
            this.emailVerificationStatus = getFieldValue(data, PRIMARY_CONTACT_EMAIL_VAL_STATUS);
            if(this.verificationDateDetails){
                let dateDetails = new Date(this.verificationDateDetails);
                this.dateDetails = ' (as at ' +dateDetails.toLocaleDateString('en-GB', { day: 'numeric', month: 'numeric', year: 'numeric' }).replace(/ /g, '/')+ ')';
            }

            //For mobile verification
            // Added by Heither Ann Ballero (SFP-30086)
            this.mobileval = getFieldValue(data, PRIMARY_CONTACT_MOBILE_FIELD);
            this.mobileInvalid = getFieldValue(data, PRIMARY_CONTACT_MOBILE_INVALID);
            this.mobileVerificationMessage = getFieldValue(data, PRIMARY_CONTACT_MOBILE_VALID_MESSAGE_FIELD);
            this.homePhoneValue = getFieldValue(data, PRIMARY_CONTACT_HOME_PHONE_FIELD);
            this.homePhoneInvalid = getFieldValue(data, PRIMARY_CONTACT_HOME_PHONE_INVALID);
            this.workPhoneValue = getFieldValue(data, PRIMARY_CONTACT_WORK_PHONE_FIELD);
            this.workPhoneInvalid = getFieldValue(data, PRIMARY_CONTACT_WORK_PHONE_INVALID);
            this.faxValue = getFieldValue(data, PRIMARY_CONTACT_FAX_FIELD);
            this.faxInvalid = getFieldValue(data, PRIMARY_CONTACT_FAX_INVALID);
            this.mobileVerificationDateDetails = getFieldValue(data, PRIMARY_CONTACT_MOBILE_VALID_DATETIME_FIELD);
            this.mobileVerificationStatus = getFieldValue(data, PRIMARY_CONTACT_MOBILE_VALID_STATUS_FIELD);
            if(this.mobileVerificationDateDetails){
                let mobileValidDate = new Date(this.mobileVerificationDateDetails);
                this.dateDetailsForMobile = ' (as at ' + mobileValidDate.toLocaleDateString('en-GB', { day: 'numeric', month: 'numeric', year: 'numeric' }).replace(/ /g, '/') +')';
            }

            this.emailInvalidPrior = this.emailInvalid;
            this.mobileInvalidPrior = this.mobileInvalid;
            this.altEmailInvalidPrior = this.altEmailInvalid;
            this.homePhoneInvalidPrior = this.homePhoneInvalid;
            this.workPhoneInvalidPrior = this.workPhoneInvalid;
            this.faxInvalidPrior = this.faxInvalid;

            //to display if there was prior callout response
            //Added by Eugene Ray Perfecio (SFP-30083)
            if(this.emailVerificationMessage){
                let style = '';
                switch(this.emailVerificationMessage){
                    case 'Successfully verified as deliverable':
                        this.emailVerificationIconName = 'utility:success';
                        this.emailVerificationVariant = 'success';
                        style = 'verification-message_verified';
                        break;
                    case 'Email is undeliverable':
                        this.emailVerificationIconName = 'utility:clear';
                        this.emailVerificationVariant = 'error';
                        style = 'verification-message_error';
                        break;
                    case 'Emails are unlikely to be delivered':
                        this.emailVerificationIconName = 'utility:warning';
                        this.emailVerificationVariant = 'warning';
                        style = 'verification-message_warning';
                        break;
                    case 'Email Address may not be in permanent use':
                        this.emailVerificationIconName = 'utility:warning';
                        this.emailVerificationVariant = 'warning';
                        style = 'verification-message_warning';
                        break;
                    case 'Verification Service unavailable':
                        this.emailVerificationIconName = 'utility:question';
                        this.emailVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;
                    case 'Email not completely verified, but it may be correct':
                        this.emailVerificationIconName = 'utility:success';
                        this.emailVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;
                    case 'Verification Service is switched off':
                        this.emailVerificationIconName = 'utility:question';
                        this.emailVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;    
                }
                this.emailVerificationStyle = 'slds-text-body--small slds-p-right_small slds-p-top_xx-small slds-p-bottom_x-small '+style;
                
                //SFP-30080 by Heither Ann Ballero
                //Call apex to calculate the months between today's date and validation date
                //The button will be disabled if the months in between is less than 12 months
                getMonthsInBetween({ 
                    //Pass the Validation Timestamp to Apex
                    verificationTimeStamp: this.verificationDateDetails
                }).then(result => {
                    console.log(result);
                    if(this.emailVerificationMessage != '' && this.emailVerificationMessage !='Verification Service unavailable' && result <=12){
                        this.disableEmailVerifyButton = true;
                    }
                })
                .catch(error => {
                    console.log('Error from ContactDetailsSectionController.calculateMonthsInBetween: ', error);
                    this.showToast('Error', 'error', 'An unexpected error occurred when calculating months in between.');
                });
            } 

            // Added by Heither Ann Ballero (SFP-30086)
            if(this.mobileVerificationMessage){
                let style = '';
                switch(this.mobileVerificationMessage){
                    case 'Successfully verified as reachable':
                        this.mobileVerificationIconName = 'utility:success';
                        this.mobileVerificationVariant = 'success';
                        style = 'verification-message_verified';
                        break;
                    case 'Mobile is unreachable':
                        this.mobileVerificationIconName = 'utility:clear';
                        this.mobileVerificationVariant = 'error';
                        style = 'verification-message_error';
                        break;
                    case 'Unable to verify mobile but it may be correct':
                        this.mobileVerificationIconName = 'utility:success';
                        this.mobileVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;
                    case 'Successfully verified but not currently reachable':
                        this.mobileVerificationIconName = 'utility:success';
                        this.mobileVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;
                    case 'Verification Service unavailable':
                        this.mobileVerificationIconName = 'utility:question';
                        this.mobileVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;
                    case 'Verification Service is switched off':
                        this.mobileVerificationIconName = 'utility:question';
                        this.mobileVerificationVariant = 'brand';
                        style = 'verification-message_bare';
                        break;    
                }
                this.mobileVerificationStyle = 'slds-text-body--small slds-p-right_small slds-p-top_xx-small slds-p-bottom_x-small '+style;
                
                //SFP-32688 by Heither Ann Ballero
                //Call apex to calculate the months between today's date and validation date
                //The button will be disabled if the months in between is less than 24 months
                getMonthsInBetween({ 
                    //Pass Mobile Validation Timestamp to Apex
                    verificationTimeStamp: this.mobileVerificationDateDetails
                }).then(result => {
                    console.log(result);
                    if(this.mobileVerificationMessage != '' && this.mobileVerificationMessage !='Verification Service unavailable' && result <=24){
                        this.disableMobileVerifyButton = true;
                    }
                })
                .catch(error => {
                    console.log('Error from ContactDetailsSectionController.calculateMonthsInBetween: ', error);
                    this.showToast('Error', 'error', 'An unexpected error occurred when calculating months in between.');
                });
            }

            //store values in an object to handle reversion when canceling edit
            //Added by Eugene Ray Perfecio (SFP-30083)
            this.storedEmailVerificationDetails = {
                emailVerificationMessage : this.emailVerificationMessage,
                emailVerificationIconName : this.emailVerificationIconName,
                emailVerificationVariant : this.emailVerificationVariant,
                emailVerificationStyle : this.emailVerificationStyle,
                dateDetails : this.dateDetails,
                emailval : this.emailval,
                altEmailValue : this.altEmailValue,
                emailVerificationStatus : this.emailVerificationStatus,
                verificationTimestamp : this.verificationDateDetails
            };

            // Added by Heither Ann Ballero (SFP-30086)
            this.storedMobileVerificationDetails = {
                mobileVerificationMessage : this.mobileVerificationMessage,
                mobileVerificationIconName : this.mobileVerificationIconName,
                mobileVerificationVariant : this.mobileVerificationVariant,
                mobileVerificationStyle : this.mobileVerificationStyle,
                dateDetailsForMobile : this.dateDetailsForMobile,
                mobileval : this.mobileval,
                mobileVerificationStatus : this.mobileVerificationStatus,
                mobileVerificationTimestamp : this.mobileVerificationDateDetails
            };

            //To remove the empty space in the layout when one of the message is populated
            if(this.emailVerificationMessage || this.mobileVerificationMessage){
                this.messageFields = true;
            }

            this.loaded = false; // stop spinner

            if(this.emailVerificationMessage){
                this.hasEmailMessage = true;
            }

            if(this.mobileVerificationMessage){
                this.hasMobileMessage = true;
            }

        } else if (error) {
            this.error = error ;
            let event = new ShowToastEvent({
                title: 'Error!',
                message: error,
                variant: 'error',
                mode: 'dismissable'
            });
            this.dispatchEvent(event);
            console.log('Error on getting account details ', error);
            this.loaded = false;
        }
    }

    //Added by Eugene Ray Perfecio (SFP-30083)
    //handle email event passe from custom email input component
    handleNewEmailValue(event){
        this.processing = true;
        
        let verificationDetails = JSON.parse(JSON.stringify(event.detail));
        //get data from event
        if(verificationDetails){
            //only get event details if email was edited
            this.isEmailValid = verificationDetails.isEmailValid;
            this.emailval = verificationDetails.email;
            if((verificationDetails.isEmailEdited && verificationDetails.isEmailValid) || verificationDetails.fromVerifyButton){
                this.emailVerificationMessage = verificationDetails.message;
                this.emailVerificationIconName = verificationDetails.iconName;
                this.emailVerificationVariant = verificationDetails.variant;
                this.emailVerificationStyle = verificationDetails.style;
                this.dateDetails = verificationDetails.dateDetails;
                this.emailVerificationStatus = verificationDetails.status;
                this.emailVerificationTimeStamp = verificationDetails.timeStamp;
            }
        }
        if(this.emailVerificationMessage){
            this.messageFields = true;
            this.hasEmailMessage = true;
        }
        
        //SFP-30080 by Heither Ann Ballero
        //Save the verification details when the Verify button is clicked
        if(verificationDetails.fromVerifyButton){  
            if(this.emailVerificationMessage){
                this.hasEmailMessage = true;
            }
            let record = {
                fields: {
                    Id: this.contactId,
                    Email_Validation_Message__c: this.emailVerificationMessage,
                    Email_Validation_Status__c: this.emailVerificationStatus,
                    Email_Validation_Timestamp__c: this.emailVerificationTimeStamp,
                },
            };
            updateRecord(record).then((result) => {
                let fields = result.fields;
                console.log('Validation details is successfully saved');
            }).catch(error => {
                console.log('Error on saving data',error.message.body);
            });
        }

        this.processing = false;
        this.disableSaveBtn = false; //Added by Eugene Ray Perfecio (SFP-39722)

    }

    //Added by Heither Ann Ballero (SFP-30086)
    //handle email event passed from custom mobile input component
    handleNewMobileValue(event){
        this.processing = true;

        let mobileVerificationDetails = JSON.parse(JSON.stringify(event.detail));
        //get data from event
        if(mobileVerificationDetails){
            //only get event details if mobile was edited
            this.isMobileValid = mobileVerificationDetails.isMobileValid;
            this.mobileval = mobileVerificationDetails.mobile;
            if((mobileVerificationDetails.isMobileEdited && mobileVerificationDetails.isMobileValid) || mobileVerificationDetails.fromVerifyButton){
                this.mobileVerificationMessage = mobileVerificationDetails.message;
                this.mobileVerificationIconName = mobileVerificationDetails.iconName;
                this.mobileVerificationVariant = mobileVerificationDetails.variant;
                this.mobileVerificationStyle = mobileVerificationDetails.style;
                this.dateDetailsForMobile = mobileVerificationDetails.dateDetails;
                this.mobileVerificationStatus = mobileVerificationDetails.status;
                this.timeStampForMobile = mobileVerificationDetails.timeStampForMobile;
            }
        }    

        if(this.mobileVerificationMessage){
            this.messageFields = true;
            this.hasMobileMessage = true;
        }

        //SFP-32688 by Heither Ann Ballero
         //Save the verification details when the Verify button is clicked
        if(mobileVerificationDetails.fromVerifyButton){ 
            if(this.mobileVerificationMessage){
                this.hasMobileMessage = true;
            } 
            let record = {
                fields: {
                    Id: this.contactId,
                    Mobile_Phone_Validation_Message__c: this.mobileVerificationMessage,
                    Mobile_Phone_Validation_Status__c: this.mobileVerificationStatus,
                    Mobile_Phone_Validation_Timestamp__c: this.timeStampForMobile,
                },
            };
            updateRecord(record).then((result) => {
                let fields = result.fields;
                console.log('Validation details is successfully saved');
            }).catch(error => {
                console.log('Error on saving data',error.message.body);
            });
        }
        this.processing = false;
        this.disableSaveBtn = false; //Added by Eugene Ray Perfecio (SFP-39722)
    }

    // enable edit mode
    enableEditMode() {
        this.isEditMode = true;
        this.verified = false;
        this.emailInvalidField = false;
    }

    // view form during load
    viewOnLoad(){
        this.viewLoad = true;
        // if(this.editAccess){
            this.disableEditBtn = false;
        // }
    }

    // toggles sections (open and close)
    toggleMainSection(){
        this.template.querySelector('[data-id="mainSection"]').classList.toggle('slds-section_hide');
        this.chevronDown = !this.chevronDown;
    }

    // edit form during load
    formOnLoad(){
        this.formLoaded = true;
        this.disableSaveBtn = false;
    }

    cancelEditMode() {
        this.formLoaded = false;
        this.isEditMode = false;
        this.verified = false;
        this.disableEditBtn = true;
        this.viewLoad = false;
        this.disableSaveBtn = true;

        //revert properties back to original ones before edit button was pressed
        //Added by Eugene Ray Perfecio (SFP-30083)
        let storedEmailVerificationDetails = JSON.parse(JSON.stringify(this.storedEmailVerificationDetails));
        if(storedEmailVerificationDetails){
            this.emailVerificationMessage = storedEmailVerificationDetails.emailVerificationMessage;
            this.emailVerificationIconName = storedEmailVerificationDetails.emailVerificationIconName;
            this.emailVerificationVariant = storedEmailVerificationDetails.emailVerificationVariant;
            this.emailVerificationStyle = storedEmailVerificationDetails.emailVerificationStyle;
            this.emailVerificationStatus = storedEmailVerificationDetails.emailVerificationStatus;
            this.dateDetails = storedEmailVerificationDetails.dateDetails;
            this.emailVerificationTimeStamp = storedEmailVerificationDetails.verificationTimestamp;
            this.emailval = storedEmailVerificationDetails.emailval;
            this.altEmailValue = storedEmailVerificationDetails.altEmailValue;
        }

        this.isEmailValid = false;
        this.isMobileValid = false;
        this.altEmailValidity = false;
        this.homePhoneValidity = false;
        this.faxValidity = false;
        this.workPhoneValidity = false;

        let storedMobileVerificationDetails = JSON.parse(JSON.stringify(this.storedMobileVerificationDetails));
        if(storedMobileVerificationDetails){
            this.mobileVerificationMessage = storedMobileVerificationDetails.mobileVerificationMessage;
            this.mobileVerificationIconName = storedMobileVerificationDetails.mobileVerificationIconName;
            this.mobileVerificationVariant = storedMobileVerificationDetails.mobileVerificationVariant;
            this.mobileVerificationStyle = storedMobileVerificationDetails.mobileVerificationStyle;
            this.mobileVerificationStatus = storedMobileVerificationDetails.mobileVerificationStatus;
            this.dateDetailsForMobile = storedMobileVerificationDetails.dateDetailsForMobile;
            this.mobileVerificationDateDetails = storedMobileVerificationDetails.mobileVerificationDateDetails;
            this.mobileval = storedMobileVerificationDetails.mobileval;
        }

        this.mobileInvalid = this.mobileInvalidPrior;
        this.emailInvalid = this.emailInvalidPrior;
        this.altEmailInvalid = this.altEmailInvalidPrior;
        this.homePhoneInvalid = this.homePhoneInvalidPrior;
        this.faxInvalid = this.faxInvalidPrior;
        this.workPhoneInvalid = this.workPhoneInvalidPrior;

        if(this.emailVerificationMessage){
            this.hasEmailMessage = true;
        }
        else{
            this.hasEmailMessage = false;
        }

        if(this.mobileVerificationMessage){
            this.hasMobileMessage = true;
        }
        else{
            this.hasMobileMessage = false;
        }
        
    }

    savePreventDefault (event) {
        this.loaded = true;
        event.preventDefault();       // stop the form from submitting
        var getfields = event.detail.fields;

        //SFP-30083 by Eugene Ray Perfecio
        //handle saving of email verification fields
        getfields.Email = this.emailval;
        getfields.Alternate_Email__c = this.altEmailValue;
        
        //SFP-36606 saving of invalid flags
        getfields.Mobile_Invalid__c = this.mobileInvalid;
        getfields.Email_Invalid__c = this.emailInvalid;
        getfields.Alternate_Email_Invalid__c = this.altEmailInvalid;
        getfields.Home_Phone_Invalid__c = this.homePhoneInvalid;
        getfields.Fax_Invalid__c = this.faxInvalid;
        getfields.Phone_Invalid__c = this.workPhoneInvalid;

        //untick only if new email is valid
        if(this.isEmailValid && this.emailval != '' && this.emailInvalidPrior){
            getfields.Email_Invalid__c = false;
        }
        //untick only if new alternate email is valid
        if(this.altEmailValidity && this.altEmailInvalidPrior){
            getfields.Alternate_Email_Invalid__c = false;
        }

        getfields.Email_Validation_Message__c = this.emailVerificationMessage;
        getfields.Email_Validation_Status__c = this.emailVerificationStatus;
        getfields.Email_Validation_Timestamp__c = this.emailVerificationTimeStamp;
        
        //Added by Heither Ann Ballero (SFP-30086)
        //Handle saving of phone fields
        getfields.MobilePhone = this.mobileval;
        getfields.HomePhone = this.homePhoneValue;
        getfields.Phone = this.workPhoneValue;
        getfields.Fax = this.faxValue;

        //Untick only if prior Mobile was invalid and new Mobile is valid
        if(this.isMobileValid && this.mobileInvalidPrior && this.mobileval !=''){
            getfields.Mobile_Invalid__c = false;
        }

        //Untick only if prior Home Phone was invalid and new Home Phone is valid
        if(this.homePhoneValidity && this.homePhoneInvalidPrior){
            getfields.Home_Phone_Invalid__c = false;
        }

        //Untick only if prior Work Phones was invalid and new Work Phones is valid
        if(this.workPhoneValidity && this.workPhoneInvalidPrior){
            getfields.Phone_Invalid__c = false;
        }

        //Untick only if prior Fax was invalid and new Fax is valid
        if(this.faxValidity && this.faxInvalidPrior){
            getfields.Fax_Invalid__c = false;
        }

        getfields.Mobile_Phone_Validation_Message__c = this.mobileVerificationMessage;
        getfields.Mobile_Phone_Validation_Status__c = this.mobileVerificationStatus;
        getfields.Mobile_Phone_Validation_Timestamp__c = this.timeStampForMobile;

        this.fields = getfields;
        this.template.querySelector('lightning-record-edit-form').submit(getfields);
    }

    handleSuccess(){
        let eventToast = new ShowToastEvent({
            title: 'Success!',
            // message: '',
            variant: 'success',
            mode: 'dismissable'
        });
        this.dispatchEvent(eventToast);
        this.loaded = false;
        this.isEditMode = false;
        // reset defaults
        this.formLoaded = false;
        this.verified = false;
        this.disableEditBtn = true;
        this.viewLoad = false;
        this.disableSaveBtn = true;

        //when email verification message is empty string, we make this into a falsy to unrender in template
        if(this.emailVerificationMessage == ''){
            this.emailVerificationMessage = null;
        }

        this.isEmailValid = false;
        this.altEmailValidity = false;
        this.isMobileValid = false;
        this.homePhoneValidity = false;
        this.faxValidity = false;
        this.workPhoneValidity = false;

        //when mobile verification message is empty string, we make this into a falsy to unrender in template
        if(this.mobileVerificationMessage == ''){
            this.mobileVerificationMessage = null;
        }

        if(this.emailVerificationMessage){
            this.hasEmailMessage = true;
        }
        else{
            this.hasEmailMessage = false;
        }

        if(this.mobileVerificationMessage){
            this.hasMobileMessage = true;
        }
        else{
            this.hasMobileMessage = false;
        }
    }

    handleError(event){
        this.loaded = false; // spinner off
        console.log('$$$ HandleError: ', JSON.parse(JSON.stringify(event.detail)));

        if(this.emailVerificationMessage){
            this.hasEmailMessage = true;
        }
        else{
            this.hasEmailMessage = false;
        }

        if(this.mobileVerificationMessage){
            this.hasMobileMessage = true;
        }
        else{
            this.hasMobileMessage = false;
        }
    }

    fieldOnchange(event){
        let altEmailValue;
        let homePhoneValue;
        let workPhoneValue;
        let faxValue;
        let allowedvalues = /^[0-9()+ ]+$/g;
        let spaces;
        let mobileInvalid;
        let emailInvalid;
        let altEmailInvalid;
        let homePhoneInvalid;
        let faxInvalid;
        let workPhoneInvalid;
        let inputArray = this.template.querySelectorAll("lightning-input");
        let inputArrayInvalidFlags = this.template.querySelectorAll("input");
        inputArray.forEach(function(element){
            if(element.name == 'altEmail'){
                altEmailValue = element.value;
                let regExpEmailformat = /^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/;
                if(event.target.name=='altEmail'){    
                    //check if email input matches with regex and does not start with "."
                    if(altEmailValue.match(regExpEmailformat) && !altEmailValue.startsWith('.')){
                        this.altEmailValidity = true;
                        element.setCustomValidity("");
                    }
                    //emails that begins with "." are invalid
                    else if(altEmailValue.startsWith('.') || !element.validity.valid){
                        this.altEmailValidity = false;
                        //error same as error on record page
                        element.setCustomValidity("Email: invalid email address: " +altEmailValue);
                    }
                    //all other format
                    else {
                        this.altEmailValidity = element.validity.valid;
                        if(this.altEmailValidity){
                            element.setCustomValidity("");
                        }
                        else{
                            element.setCustomValidity("Email: invalid email address: " +altEmailValue);
                        }
                    }
                }            
            }
            //Added by Heither Ann Ballero (SFP-30086)
            if(element.name == 'homePhone'){
            homePhoneValue = element.value;
                let homePhone = homePhoneValue.replace(/[+() ]/g, '');
                spaces = homePhoneValue.split(" ").length - 1;
                //Check if the home phone value contains allowed values and it has 6 or more digits
                if(homePhoneValue.match(allowedvalues) && homePhone.length >= 6  && spaces <=1 && event.target.name=='homePhone'){
                    this.homePhoneValidity = true;
                }
            }
            if(element.name == 'workPhone'){
                workPhoneValue = element.value;
                let workPhone = workPhoneValue.replace(/[+() ]/g, '');
                spaces = workPhoneValue.split(" ").length - 1;
                //Check if the work phone value contains allowed values and it has 6 or more digits
                if(workPhoneValue.match(allowedvalues) && workPhone.length >= 6  && spaces <=1 && event.target.name=='workPhone'){
                    this.workPhoneValidity = true;
                }
            }
            if(element.name == 'fax'){
                faxValue = element.value;
                let fax = faxValue.replace(/[+() ]/g, '');
                //Check if the fax value contains allowed values and it has 6 or more digits
                spaces = faxValue.split(" ").length - 1;
                if(faxValue.match(allowedvalues) && fax.length >= 6  && spaces <=1 && event.target.name=='fax'){
                    this.faxValidity = true;
                }
            }
        }, this);
        //SFP-36606 Saving of invalid flags
        inputArrayInvalidFlags.forEach(function(element){
            if(element.name == 'mobInvalid' ){
                mobileInvalid = element.checked;
            }
            if(element.name == 'emlInvalid' ){
                emailInvalid = element.checked;
            }
            if(element.name == 'altInvalid' ){
                altEmailInvalid = element.checked;
            }
            if(element.name == 'homeInvalid' ){
                homePhoneInvalid = element.checked;
            }
            if(element.name == 'fxInvalid' ){
                faxInvalid = element.checked;
            }
            if(element.name == 'workInvalid' ){
                workPhoneInvalid = element.checked;
            }
        });

        this.altEmailValue = altEmailValue;
        this.homePhoneValue = homePhoneValue;
        this.workPhoneValue = workPhoneValue;
        this.faxValue = faxValue;
        //SFP-36606 
        this.mobileInvalid = mobileInvalid;
        this.emailInvalid = emailInvalid;
        this.altEmailInvalid = altEmailInvalid;
        this.homePhoneInvalid = homePhoneInvalid;
        this.faxInvalid = faxInvalid;
        this.workPhoneInvalid = workPhoneInvalid;
        
    }

    //Added by Eugene Ray Perfecio (SFP-39722)
    //disable save button when mobile and email fields are focused
    //only enable them again when users move away from the field and Experian callout is successful 
    disableSaveButton(event){
        console.log('$$$ event here');
        this.disableSaveBtn = true;
        console.log('$$$ disableSaveBtn ', this.disableSaveBtn);
    }
}